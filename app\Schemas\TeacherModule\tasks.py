from pydantic import BaseModel, computed_field
from uuid import UUID
from datetime import datetime
from typing import List, Optional
from enum import Enum
from config.config import settings


# Enums
class TaskStatusEnum(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


# Base Models
class SubjectBase(BaseModel):
    name: str

    class Config:
        from_attributes = True


class ChapterBase(BaseModel):
    name: str
    description: str

    class Config:
        from_attributes = True


class TopicBase(BaseModel):
    name: str
    description: str

    class Config:
        from_attributes = True


class SubTopicBase(BaseModel):
    name: str
    description: str

    class Config:
        from_attributes = True


class TaskBase(BaseModel):
    name: str
    description: Optional[str] = None
    status: TaskStatusEnum = TaskStatusEnum.PENDING
    deadline: Optional[datetime] = None
    accept_after_deadline: bool = False

    class Config:
        from_attributes = True


# Response Models
class SubjectOut(SubjectBase):
    id: UUID

    class Config:
        from_attributes = True


class SubTopicOut(SubTopicBase):
    id: UUID
    topic_id: UUID

    class Config:
        from_attributes = True


class TopicOut(TopicBase):
    id: UUID
    chapter_id: UUID
    subtopics: List[SubTopicOut] = []

    class Config:
        from_attributes = True


class ChapterOut(ChapterBase):
    id: UUID
    subject_id: UUID
    topics: List[TopicOut] = []

    class Config:
        from_attributes = True


class TaskAttachmentOut(BaseModel):
    id: UUID
    file_url: str
    file_name: Optional[str] = None
    task_id: UUID
    student_id: Optional[UUID] = None  # NULL for teacher attachments, UUID for student submissions

    @computed_field
    @property
    def download_url(self) -> str:
        """Get the full download URL for the attachment"""
        return f"{settings.STATIC_FILES_URL}/{self.file_url}"

    class Config:
        from_attributes = True


class TaskStudentOut(BaseModel):
    task_id: UUID
    student_id: UUID
    submission_date: Optional[datetime] = None
    grade: Optional[int] = None

    class Config:
        from_attributes = True


class TaskClassroomOut(BaseModel):
    task_id: UUID
    classroom_id: UUID

    class Config:
        from_attributes = True


class TaskClassroomStudentOut(BaseModel):
    task_id: UUID
    classroom_id: UUID
    student_id: UUID

    class Config:
        from_attributes = True


class TaskChapterOut(BaseModel):
    task_id: UUID
    chapter_id: UUID

    class Config:
        from_attributes = True


class TaskTopicOut(BaseModel):
    task_id: UUID
    topic_id: UUID

    class Config:
        from_attributes = True


class TaskSubTopicOut(BaseModel):
    task_id: UUID
    subtopic_id: UUID

    class Config:
        from_attributes = True


# Main Task Response Model
class TaskOut(TaskBase):
    id: UUID
    subject_id: Optional[UUID] = None
    subject: Optional[SubjectOut] = None
    attachments: List[TaskAttachmentOut] = []
    students: List[TaskStudentOut] = []
    classrooms: List[TaskClassroomOut] = []
    classroom_students: List[TaskClassroomStudentOut] = []
    chapters: List[TaskChapterOut] = []
    topics: List[TaskTopicOut] = []
    subtopics: List[TaskSubTopicOut] = []
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Create Models
class SubjectCreate(SubjectBase):
    pass


class ChapterCreate(ChapterBase):
    subject_id: UUID


class TopicCreate(TopicBase):
    chapter_id: UUID


class SubTopicCreate(SubTopicBase):
    topic_id: UUID


class TaskCreate(TaskBase):
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []


class TaskCreateWithAssignments(TaskBase):
    """Create task with simultaneous assignments"""
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []
    classroom_ids: List[UUID] = []
    student_ids: List[UUID] = []
    classroom_student_assignments: List[dict] = []  # [{"classroom_id": UUID, "student_id": UUID}]


class TaskCreateForStudent(TaskBase):
    """Create task and assign to a single student"""
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []
    student_id: UUID


class TaskCreateForClassroom(TaskBase):
    """Create task and assign to a single classroom"""
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []
    classroom_id: UUID


class TaskCreateForMultipleStudents(TaskBase):
    """Create task and assign to multiple students"""
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []
    student_ids: List[UUID]


class TaskCreateForMultipleClassrooms(TaskBase):
    """Create task and assign to multiple classrooms"""
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []
    classroom_ids: List[UUID]


# Update Models
class SubjectUpdate(BaseModel):
    name: Optional[str] = None

    class Config:
        from_attributes = True


class ChapterUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None

    class Config:
        from_attributes = True


class TopicUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None

    class Config:
        from_attributes = True


class SubTopicUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None

    class Config:
        from_attributes = True


class TaskUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatusEnum] = None
    subject_id: Optional[UUID] = None
    deadline: Optional[datetime] = None
    accept_after_deadline: Optional[bool] = None

    class Config:
        from_attributes = True


# List Response Models
class SubjectListOut(BaseModel):
    subjects: List[SubjectOut]
    total: int

    class Config:
        from_attributes = True


class ChapterListOut(BaseModel):
    chapters: List[ChapterOut]
    total: int

    class Config:
        from_attributes = True


class TopicListOut(BaseModel):
    topics: List[TopicOut]
    total: int

    class Config:
        from_attributes = True


class SubTopicListOut(BaseModel):
    subtopics: List[SubTopicOut]
    total: int

    class Config:
        from_attributes = True


class TaskListOut(BaseModel):
    tasks: List[TaskOut]
    total: int

    class Config:
        from_attributes = True


class StudentTaskAttachmentOut(BaseModel):
    id: UUID
    file_url: str
    file_name: Optional[str] = None
    task_id: UUID
    student_id: UUID
    submission_date: Optional[datetime] = None

    @computed_field
    @property
    def download_url(self) -> str:
        """Get the full download URL for the attachment"""
        return f"{settings.STATIC_FILES_URL}/{self.file_url}"

    class Config:
        from_attributes = True


class StudentTaskMinimalOut(BaseModel):
    """Minimal task response for students - used in lists"""
    id: UUID
    name: str
    description: Optional[str] = None
    deadline: Optional[datetime] = None
    status: TaskStatusEnum
    accept_after_deadline: bool = False
    subject: Optional[SubjectOut] = None

    class Config:
        from_attributes = True


class StudentTaskDetailedOut(BaseModel):
    """Detailed task response for students - used for individual task view"""
    id: UUID
    name: str
    description: Optional[str] = None
    status: TaskStatusEnum
    deadline: Optional[datetime] = None
    accept_after_deadline: bool = False
    created_at: datetime
    updated_at: datetime
    subject: Optional[SubjectOut] = None
    chapters: List[ChapterOut] = []
    topics: List[TopicOut] = []
    subtopics: List[SubTopicOut] = []
    teacher_attachments: List[TaskAttachmentOut] = []
    my_attachments: List[StudentTaskAttachmentOut] = []

    class Config:
        from_attributes = True


class StudentTaskOut(BaseModel):
    """Student task response model with detailed information"""
    id: UUID
    name: str
    description: Optional[str] = None
    status: TaskStatusEnum
    deadline: Optional[datetime] = None
    accept_after_deadline: bool = False
    created_at: datetime
    updated_at: datetime
    subject_id: Optional[UUID] = None
    subject: Optional[SubjectOut] = None
    chapters: List[ChapterOut] = []
    topics: List[TopicOut] = []
    subtopics: List[SubTopicOut] = []

    class Config:
        from_attributes = True


class MyTasks(BaseModel):
    """Tasks response for students with their attachments"""
    tasks: List[StudentTaskOut]
    total: int
    student_attachments: List[StudentTaskAttachmentOut] = []

    class Config:
        from_attributes = True


class MyTasksMinimal(BaseModel):
    """Minimal tasks response for students"""
    tasks: List[StudentTaskMinimalOut]
    total: int

    class Config:
        from_attributes = True


# Specialized Response Models
class TaskWithDetailsOut(TaskOut):
    """Task with full details including nested subject, chapters, topics, subtopics, and attachments"""
    subject: Optional[SubjectOut] = None
    teacher_attachments: List[TaskAttachmentOut] = []
    student_attachments: List[StudentTaskAttachmentOut] = []
    # You can add more detailed nested objects here if needed

    class Config:
        from_attributes = True


class SubjectWithHierarchyOut(SubjectOut):
    """Subject with full chapter/topic/subtopic hierarchy"""
    chapters: List[ChapterOut] = []

    class Config:
        from_attributes = True


class ChapterWithTopicsOut(ChapterOut):
    """Chapter with topics and subtopics"""
    topics: List[TopicOut] = []

    class Config:
        from_attributes = True

class TopicWithSubTopicsOut(TopicOut):
    """Topic with subtopics"""
    subtopics: List[SubTopicOut] = []

    class Config:
        from_attributes = True 
