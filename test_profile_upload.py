#!/usr/bin/env python3
"""
Simple test script to verify profile picture upload functionality
"""

import requests
import json
from pathlib import Path

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_IMAGE_PATH = "test_image.png"

def create_test_image():
    """Create a simple test image using PIL"""
    try:
        from PIL import Image
        
        # Create a simple 100x100 red square
        img = Image.new('RGB', (100, 100), color='red')
        img.save(TEST_IMAGE_PATH)
        print(f"✅ Test image created: {TEST_IMAGE_PATH}")
        return True
    except ImportError:
        print("❌ PIL not available, cannot create test image")
        return False

def test_profile_upload():
    """Test profile picture upload endpoint"""
    
    # First, create a test image
    if not create_test_image():
        return
    
    # Test data - you'll need to replace this with a valid JWT token
    # You can get this by logging in through the API
    test_token = "your_jwt_token_here"
    
    headers = {
        "Authorization": f"Bearer {test_token}"
    }
    
    # Test the upload
    try:
        with open(TEST_IMAGE_PATH, 'rb') as f:
            files = {'file': (TEST_IMAGE_PATH, f, 'image/png')}
            
            response = requests.post(
                f"{BASE_URL}/api/files/profile-picture",
                headers=headers,
                files=files
            )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Profile picture upload successful!")
            print(f"Profile Picture URL: {data.get('profile_picture_url')}")
            print(f"Thumbnail URL: {data.get('thumbnail_url')}")
        else:
            print("❌ Upload failed")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure it's running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        # Clean up test image
        if Path(TEST_IMAGE_PATH).exists():
            Path(TEST_IMAGE_PATH).unlink()
            print(f"🧹 Cleaned up test image: {TEST_IMAGE_PATH}")

def test_health_check():
    """Test if the server is running"""
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            print("✅ Server is running and healthy")
            return True
        else:
            print(f"⚠️ Server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server")
        return False

if __name__ == "__main__":
    print("🧪 Testing Profile Picture Upload API")
    print("=" * 50)
    
    # First check if server is running
    if test_health_check():
        print("\n📝 Note: You need to replace 'your_jwt_token_here' with a valid JWT token")
        print("   You can get this by logging in through the API first")
        print("\n🔧 To test with a real token:")
        print("   1. Login via POST /api/auth/login")
        print("   2. Copy the access_token from the response")
        print("   3. Replace 'your_jwt_token_here' in this script")
        print("   4. Run this script again")
        
        # Uncomment the line below to test upload (after adding a valid token)
        # test_profile_upload()
    else:
        print("❌ Server is not running. Start it with: python -m uvicorn main:app --reload")
