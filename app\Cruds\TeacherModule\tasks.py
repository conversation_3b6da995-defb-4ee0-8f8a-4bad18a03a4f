from uuid import uuid4, UUI<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException
from typing import List, Optional
from datetime import datetime

# Import Models
from Models.Tasks import Task, TaskAttachment, StudentTaskAttachment, TaskStudents, TaskClassroom, TaskClassroomStudent, TaskChapter, TaskTopic, TaskSubTopic, TaskStatus
from Models.Chapter import Chapter, Topic, SubTopic
from Models.users import Subject, User
from Models.Classroom import Classroom

# Import Schemas
from Schemas.TeacherModule.tasks import (
    MyTasksMinimal, StudentTaskDetailedOut, StudentTaskMinimalOut, StudentTaskOut, MyTasks, TaskCreate, TaskOut, TaskUpdate, TaskListOut, TaskWithDetailsOut,
    SubjectCreate, SubjectOut, SubjectUpdate, SubjectListOut, SubjectWithHierarchyOut,
    ChapterCreate, ChapterOut, ChapterUpdate, ChapterListOut, ChapterWithTopicsOut,
    TopicCreate, TopicOut, TopicUpdate, TopicListOut, TopicWithSubTopicsOut,
    SubTopicCreate, SubTopicOut, SubTopicUpdate, SubTopicListOut,
    TaskAttachmentOut, StudentTaskAttachmentOut, TaskStudentOut, TaskClassroomOut, TaskClassroomStudentOut,
    TaskChapterOut, TaskTopicOut, TaskSubTopicOut, TaskCreateWithAssignments,
    TaskCreateForStudent, TaskCreateForClassroom, TaskCreateForMultipleStudents, TaskCreateForMultipleClassrooms
)


# ==================== SUBJECT CRUD ====================

def create_subject(db: Session, subject_create: SubjectCreate) -> SubjectOut:
    """Create a new subject"""
    try:
        # Check if subject with same name already exists
        existing_subject = db.query(Subject).filter(Subject.name == subject_create.name).first()
        if existing_subject:
            raise HTTPException(status_code=400, detail="Subject with this name already exists.")

        new_subject = Subject(
            id=uuid4(),
            name=subject_create.name
        )
        db.add(new_subject)
        db.commit()
        db.refresh(new_subject)
        return SubjectOut.model_validate(new_subject)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_subject_by_id(db: Session, subject_id: UUID) -> SubjectOut:
    """Get subject by ID"""
    subject = db.query(Subject).filter(Subject.id == subject_id).first()
    if not subject:
        raise HTTPException(status_code=404, detail="Subject not found.")
    return SubjectOut.model_validate(subject)


def get_all_subjects(db: Session, skip: int = 0, limit: int = 100) -> SubjectListOut:
    """Get all subjects with pagination"""
    subjects = db.query(Subject).offset(skip).limit(limit).all()
    total = db.query(Subject).count()
    return SubjectListOut(
        subjects=[SubjectOut.model_validate(subject) for subject in subjects],
        total=total
    )


def update_subject(db: Session, subject_id: UUID, subject_update: SubjectUpdate) -> SubjectOut:
    """Update subject"""
    try:
        subject = db.query(Subject).filter(Subject.id == subject_id).first()
        if not subject:
            raise HTTPException(status_code=404, detail="Subject not found.")

        if subject_update.name is not None:
            # Check if new name conflicts with existing subject
            existing_subject = db.query(Subject).filter(
                Subject.name == subject_update.name,
                Subject.id != subject_id
            ).first()
            if existing_subject:
                raise HTTPException(status_code=400, detail="Subject with this name already exists.")
            subject.name = subject_update.name

        db.commit()
        db.refresh(subject)
        return SubjectOut.model_validate(subject)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def delete_subject(db: Session, subject_id: UUID) -> None:
    """Delete subject"""
    try:
        subject = db.query(Subject).filter(Subject.id == subject_id).first()
        if not subject:
            raise HTTPException(status_code=404, detail="Subject not found.")
        
        db.delete(subject)
        db.commit()

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_subject_with_hierarchy(db: Session, subject_id: UUID) -> SubjectWithHierarchyOut:
    """Get subject with full chapter/topic/subtopic hierarchy"""
    subject = db.query(Subject).filter(Subject.id == subject_id).first()
    if not subject:
        raise HTTPException(status_code=404, detail="Subject not found.")
    return SubjectWithHierarchyOut.model_validate(subject)


# ==================== CHAPTER CRUD ====================

def create_chapter(db: Session, chapter_create: ChapterCreate) -> ChapterOut:
    """Create a new chapter"""
    try:
        # Verify subject exists
        subject = db.query(Subject).filter(Subject.id == chapter_create.subject_id).first()
        if not subject:
            raise HTTPException(status_code=404, detail="Subject not found.")

        new_chapter = Chapter(
            id=uuid4(),
            name=chapter_create.name,
            description=chapter_create.description,
            subject_id=chapter_create.subject_id
        )
        db.add(new_chapter)
        db.commit()
        db.refresh(new_chapter)
        return ChapterOut.from_orm(new_chapter)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_chapter_by_id(db: Session, chapter_id: UUID) -> ChapterOut:
    """Get chapter by ID"""
    chapter = db.query(Chapter).filter(Chapter.id == chapter_id).first()
    if not chapter:
        raise HTTPException(status_code=404, detail="Chapter not found.")
    return ChapterOut.model_validate(chapter)


def get_chapters_by_subject(db: Session, subject_id: UUID, skip: int = 0, limit: int = 100) -> ChapterListOut:
    """Get chapters by subject ID"""
    chapters = db.query(Chapter).filter(Chapter.subject_id == subject_id).offset(skip).limit(limit).all()
    total = db.query(Chapter).filter(Chapter.subject_id == subject_id).count()
    return ChapterListOut(
        chapters=[ChapterOut.from_orm(chapter) for chapter in chapters],
        total=total
    )


def get_all_chapters(db: Session, skip: int = 0, limit: int = 100) -> ChapterListOut:
    """Get all chapters with pagination"""
    chapters = db.query(Chapter).offset(skip).limit(limit).all()
    total = db.query(Chapter).count()
    return ChapterListOut(
        chapters=[ChapterOut.from_orm(chapter) for chapter in chapters],
        total=total
    )


def update_chapter(db: Session, chapter_id: UUID, chapter_update: ChapterUpdate) -> ChapterOut:
    """Update chapter"""
    try:
        chapter = db.query(Chapter).filter(Chapter.id == chapter_id).first()
        if not chapter:
            raise HTTPException(status_code=404, detail="Chapter not found.")

        if chapter_update.name is not None:
            chapter.name = chapter_update.name
        if chapter_update.description is not None:
            chapter.description = chapter_update.description

        db.commit()
        db.refresh(chapter)
        return ChapterOut.from_orm(chapter)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def delete_chapter(db: Session, chapter_id: UUID) -> None:
    """Delete chapter"""
    try:
        chapter = db.query(Chapter).filter(Chapter.id == chapter_id).first()
        if not chapter:
            raise HTTPException(status_code=404, detail="Chapter not found.")
        
        db.delete(chapter)
        db.commit()

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_chapter_with_topics(db: Session, chapter_id: UUID) -> ChapterWithTopicsOut:
    """Get chapter with topics and subtopics"""
    chapter = db.query(Chapter).filter(Chapter.id == chapter_id).first()
    if not chapter:
        raise HTTPException(status_code=404, detail="Chapter not found.")
    return ChapterWithTopicsOut.model_validate(chapter)


# ==================== TOPIC CRUD ====================

def create_topic(db: Session, topic_create: TopicCreate) -> TopicOut:
    """Create a new topic"""
    try:
        # Verify chapter exists
        chapter = db.query(Chapter).filter(Chapter.id == topic_create.chapter_id).first()
        if not chapter:
            raise HTTPException(status_code=404, detail="Chapter not found.")

        new_topic = Topic(
            id=uuid4(),
            name=topic_create.name,
            description=topic_create.description,
            chapter_id=topic_create.chapter_id
        )
        db.add(new_topic)
        db.commit()
        db.refresh(new_topic)
        return TopicOut.from_orm(new_topic)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_topic_by_id(db: Session, topic_id: UUID) -> TopicOut:
    """Get topic by ID"""
    topic = db.query(Topic).filter(Topic.id == topic_id).first()
    if not topic:
        raise HTTPException(status_code=404, detail="Topic not found.")
    return TopicOut.from_orm(topic)


def get_topics_by_chapter(db: Session, chapter_id: UUID, skip: int = 0, limit: int = 100) -> TopicListOut:
    """Get topics by chapter ID"""
    topics = db.query(Topic).filter(Topic.chapter_id == chapter_id).offset(skip).limit(limit).all()
    total = db.query(Topic).filter(Topic.chapter_id == chapter_id).count()
    return TopicListOut(
        topics=[TopicOut.from_orm(topic) for topic in topics],
        total=total
    )


def get_all_topics(db: Session, skip: int = 0, limit: int = 100) -> TopicListOut:
    """Get all topics with pagination"""
    topics = db.query(Topic).offset(skip).limit(limit).all()
    total = db.query(Topic).count()
    return TopicListOut(
        topics=[TopicOut.from_orm(topic) for topic in topics],
        total=total
    )


def update_topic(db: Session, topic_id: UUID, topic_update: TopicUpdate) -> TopicOut:
    """Update topic"""
    try:
        topic = db.query(Topic).filter(Topic.id == topic_id).first()
        if not topic:
            raise HTTPException(status_code=404, detail="Topic not found.")

        if topic_update.name is not None:
            topic.name = topic_update.name
        if topic_update.description is not None:
            topic.description = topic_update.description

        db.commit()
        db.refresh(topic)
        return TopicOut.from_orm(topic)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def delete_topic(db: Session, topic_id: UUID) -> None:
    """Delete topic"""
    try:
        topic = db.query(Topic).filter(Topic.id == topic_id).first()
        if not topic:
            raise HTTPException(status_code=404, detail="Topic not found.")
        
        db.delete(topic)
        db.commit()

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_topic_with_subtopics(db: Session, topic_id: UUID) -> TopicWithSubTopicsOut:
    """Get topic with subtopics"""
    topic = db.query(Topic).filter(Topic.id == topic_id).first()
    if not topic:
        raise HTTPException(status_code=404, detail="Topic not found.")
    return TopicWithSubTopicsOut.model_validate(topic)


# ==================== SUBTOPIC CRUD ====================

def create_subtopic(db: Session, subtopic_create: SubTopicCreate) -> SubTopicOut:
    """Create a new subtopic"""
    try:
        # Verify topic exists
        topic = db.query(Topic).filter(Topic.id == subtopic_create.topic_id).first()
        if not topic:
            raise HTTPException(status_code=404, detail="Topic not found.")

        new_subtopic = SubTopic(
            id=uuid4(),
            name=subtopic_create.name,
            description=subtopic_create.description,
            topic_id=subtopic_create.topic_id
        )
        db.add(new_subtopic)
        db.commit()
        db.refresh(new_subtopic)
        return SubTopicOut.from_orm(new_subtopic)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_subtopic_by_id(db: Session, subtopic_id: UUID) -> SubTopicOut:
    """Get subtopic by ID"""
    subtopic = db.query(SubTopic).filter(SubTopic.id == subtopic_id).first()
    if not subtopic:
        raise HTTPException(status_code=404, detail="SubTopic not found.")
    return SubTopicOut.from_orm(subtopic)


def get_subtopics_by_topic(db: Session, topic_id: UUID, skip: int = 0, limit: int = 100) -> SubTopicListOut:
    """Get subtopics by topic ID"""
    subtopics = db.query(SubTopic).filter(SubTopic.topic_id == topic_id).offset(skip).limit(limit).all()
    total = db.query(SubTopic).filter(SubTopic.topic_id == topic_id).count()
    return SubTopicListOut(
        subtopics=[SubTopicOut.from_orm(subtopic) for subtopic in subtopics],
        total=total
    )


def get_all_subtopics(db: Session, skip: int = 0, limit: int = 100) -> SubTopicListOut:
    """Get all subtopics with pagination"""
    subtopics = db.query(SubTopic).offset(skip).limit(limit).all()
    total = db.query(SubTopic).count()
    return SubTopicListOut(
        subtopics=[SubTopicOut.from_orm(subtopic) for subtopic in subtopics],
        total=total
    )


def update_subtopic(db: Session, subtopic_id: UUID, subtopic_update: SubTopicUpdate) -> SubTopicOut:
    """Update subtopic"""
    try:
        subtopic = db.query(SubTopic).filter(SubTopic.id == subtopic_id).first()
        if not subtopic:
            raise HTTPException(status_code=404, detail="SubTopic not found.")

        if subtopic_update.name is not None:
            subtopic.name = subtopic_update.name
        if subtopic_update.description is not None:
            subtopic.description = subtopic_update.description

        db.commit()
        db.refresh(subtopic)
        return SubTopicOut.from_orm(subtopic)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def delete_subtopic(db: Session, subtopic_id: UUID) -> None:
    """Delete subtopic"""
    try:
        subtopic = db.query(SubTopic).filter(SubTopic.id == subtopic_id).first()
        if not subtopic:
            raise HTTPException(status_code=404, detail="SubTopic not found.")
        
        db.delete(subtopic)
        db.commit()

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


# ==================== TASK CRUD ====================

def create_task(db: Session, task_create: TaskCreate) -> TaskOut:
    """Create a new task"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.model_validate(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_task_by_id(db: Session, task_id: UUID) -> TaskOut:
    """Get task by ID with attachments included"""
    try:
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Get attachments for this task
        task_attachments = db.query(TaskAttachment).filter(TaskAttachment.task_id == task_id).all()

        # Convert task to dict and add attachments
        task_data = TaskOut.model_validate(task)
        task_dict = task_data.dict()
        task_dict['attachments'] = [TaskAttachmentOut.model_validate(att) for att in task_attachments]

        return TaskOut(**task_dict)

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_task_with_details(db: Session, task_id: UUID) -> TaskWithDetailsOut:
    """Get a task with all its details including attachments"""
    try:
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Get all attachments
        task_attachments = db.query(TaskAttachment).filter(TaskAttachment.task_id == task_id).all()
        student_attachments = db.query(StudentTaskAttachment).filter(StudentTaskAttachment.task_id == task_id).all()

        # Convert to TaskWithDetailsOut
        task_data = TaskOut.model_validate(task)

        return TaskWithDetailsOut(
            **task_data.dict(),
            teacher_attachments=[TaskAttachmentOut.model_validate(att) for att in task_attachments],
            student_attachments=[StudentTaskAttachmentOut.model_validate(att) for att in student_attachments]
        )

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_tasks_by_subject(db: Session, subject_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks by subject ID"""
    tasks = db.query(Task).filter(Task.subject_id == subject_id).offset(skip).limit(limit).all()
    total = db.query(Task).filter(Task.subject_id == subject_id).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_tasks_by_chapter(db: Session, chapter_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks by chapter ID through junction table"""
    task_chapters = db.query(TaskChapter).filter(TaskChapter.chapter_id == chapter_id).offset(skip).limit(limit).all()
    task_ids = [tc.task_id for tc in task_chapters]
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()
    total = db.query(TaskChapter).filter(TaskChapter.chapter_id == chapter_id).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_tasks_by_topic(db: Session, topic_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks by topic ID through junction table"""
    task_topics = db.query(TaskTopic).filter(TaskTopic.topic_id == topic_id).offset(skip).limit(limit).all()
    task_ids = [tt.task_id for tt in task_topics]
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()
    total = db.query(TaskTopic).filter(TaskTopic.topic_id == topic_id).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_tasks_by_subtopic(db: Session, subtopic_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks by subtopic ID through junction table"""
    task_subtopics = db.query(TaskSubTopic).filter(TaskSubTopic.subtopic_id == subtopic_id).offset(skip).limit(limit).all()
    task_ids = [ts.task_id for ts in task_subtopics]
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()
    total = db.query(TaskSubTopic).filter(TaskSubTopic.subtopic_id == subtopic_id).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_all_tasks(db: Session, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get all tasks with pagination"""
    tasks = db.query(Task).offset(skip).limit(limit).all()
    total = db.query(Task).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def update_task(db: Session, task_id: UUID, task_update: TaskUpdate) -> TaskOut:
    """Update task"""
    try:
        with db.begin():
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            # Verify subject exists if provided
            if task_update.subject_id is not None:
                if task_update.subject_id:
                    subject = db.query(Subject).filter(Subject.id == task_update.subject_id).first()
                    if not subject:
                        raise HTTPException(status_code=404, detail="Subject not found.")
                task.subject_id = task_update.subject_id

            if task_update.name is not None:
                task.name = task_update.name
            if task_update.description is not None:
                task.description = task_update.description
            if task_update.status is not None:
                task.status = TaskStatus(task_update.status.value)
            if task_update.deadline is not None:
                task.deadline = task_update.deadline
            if task_update.accept_after_deadline is not None:
                task.accept_after_deadline = task_update.accept_after_deadline

            db.flush()
            db.refresh(task)
            return TaskOut.from_orm(task)

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def delete_task(db: Session, task_id: UUID) -> None:
    """Delete task"""
    try:
        with db.begin():
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")
            
            db.delete(task)
            db.flush()

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


# ==================== TASK CLASSROOM & STUDENT MANAGEMENT ====================

def assign_task_to_classroom(db: Session, task_id: UUID, classroom_id: UUID) -> TaskClassroomOut:
    """Assign task to an entire classroom"""
    try:
        with db.begin():
            # Verify task and classroom exist
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
            if not classroom:
                raise HTTPException(status_code=404, detail="Classroom not found.")

            # Check if relationship already exists
            existing = db.query(TaskClassroom).filter(
                TaskClassroom.task_id == task_id,
                TaskClassroom.classroom_id == classroom_id
            ).first()
            if existing:
                raise HTTPException(status_code=400, detail="Task already assigned to this classroom.")

            task_classroom = TaskClassroom(
                task_id=task_id,
                classroom_id=classroom_id
            )
            db.add(task_classroom)
            db.flush()
            return TaskClassroomOut.from_orm(task_classroom)

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def remove_task_from_classroom(db: Session, task_id: UUID, classroom_id: UUID) -> None:
    """Remove task assignment from classroom"""
    try:
        with db.begin():
            task_classroom = db.query(TaskClassroom).filter(
                TaskClassroom.task_id == task_id,
                TaskClassroom.classroom_id == classroom_id
            ).first()
            if not task_classroom:
                raise HTTPException(status_code=404, detail="Task not assigned to this classroom.")

            db.delete(task_classroom)
            db.flush()

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def assign_task_to_student(db: Session, task_id: UUID, student_id: UUID) -> TaskStudentOut:
    """Assign task to an individual student"""
    try:
        with db.begin():
            # Verify task and student exist
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            student = db.query(User).filter(User.id == student_id, User.user_type == "student").first()
            if not student:
                raise HTTPException(status_code=404, detail="Student not found.")

            # Check if relationship already exists
            existing = db.query(TaskStudents).filter(
                TaskStudents.task_id == task_id,
                TaskStudents.student_id == student_id
            ).first()
            if existing:
                raise HTTPException(status_code=400, detail="Task already assigned to this student.")

            task_student = TaskStudents(
                task_id=task_id,
                student_id=student_id
            )
            db.add(task_student)
            db.flush()
            return TaskStudentOut.from_orm(task_student)

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def remove_task_from_student(db: Session, task_id: UUID, student_id: UUID) -> None:
    """Remove task assignment from student"""
    try:
        with db.begin():
            task_student = db.query(TaskStudents).filter(
                TaskStudents.task_id == task_id,
                TaskStudents.student_id == student_id
            ).first()
            if not task_student:
                raise HTTPException(status_code=404, detail="Task not assigned to this student.")

            db.delete(task_student)
            db.flush()

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def assign_task_to_classroom_student(db: Session, task_id: UUID, classroom_id: UUID, student_id: UUID) -> TaskClassroomStudentOut:
    """Assign task to a specific student within a classroom"""
    try:
        with db.begin():
            # Verify task, classroom, and student exist
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
            if not classroom:
                raise HTTPException(status_code=404, detail="Classroom not found.")

            student = db.query(User).filter(User.id == student_id, User.user_type == "student").first()
            if not student:
                raise HTTPException(status_code=404, detail="Student not found.")

            # Check if relationship already exists
            existing = db.query(TaskClassroomStudent).filter(
                TaskClassroomStudent.task_id == task_id,
                TaskClassroomStudent.classroom_id == classroom_id,
                TaskClassroomStudent.student_id == student_id
            ).first()
            if existing:
                raise HTTPException(status_code=400, detail="Task already assigned to this student in this classroom.")

            task_classroom_student = TaskClassroomStudent(
                task_id=task_id,
                classroom_id=classroom_id,
                student_id=student_id
            )
            db.add(task_classroom_student)
            db.flush()
            return TaskClassroomStudentOut.from_orm(task_classroom_student)

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def remove_task_from_classroom_student(db: Session, task_id: UUID, classroom_id: UUID, student_id: UUID) -> None:
    """Remove task assignment from a specific student within a classroom"""
    try:
        with db.begin():
            task_classroom_student = db.query(TaskClassroomStudent).filter(
                TaskClassroomStudent.task_id == task_id,
                TaskClassroomStudent.classroom_id == classroom_id,
                TaskClassroomStudent.student_id == student_id
            ).first()
            if not task_classroom_student:
                raise HTTPException(status_code=404, detail="Task not assigned to this student in this classroom.")

            db.delete(task_classroom_student)
            db.flush()

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_tasks_by_classroom(db: Session, classroom_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks assigned to a specific classroom"""
    task_classrooms = db.query(TaskClassroom).filter(TaskClassroom.classroom_id == classroom_id).offset(skip).limit(limit).all()
    task_ids = [tc.task_id for tc in task_classrooms]
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()
    total = db.query(TaskClassroom).filter(TaskClassroom.classroom_id == classroom_id).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_tasks_by_student(db: Session, student_id: UUID, skip: int = 0, limit: int = 100) -> MyTasks:
    """Get tasks assigned to a specific student with only their own attachments for security"""
    # Get task assignments for the student
    task_students = db.query(TaskStudents).filter(TaskStudents.student_id == student_id).offset(skip).limit(limit).all()
    task_ids = [ts.task_id for ts in task_students]

    if not task_ids:
        return MyTasks(tasks=[], total=0, student_attachments=[])

    # Get the tasks
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()

    # Get student's own attachments for these tasks
    student_attachments = db.query(StudentTaskAttachment).filter(
        StudentTaskAttachment.student_id == student_id,
        StudentTaskAttachment.task_id.in_(task_ids)
    ).all()

    # Create task objects with proper relationship handling
    task_objects = []
    for task in tasks:
        # Get chapters for this task
        task_chapters = db.query(TaskChapter).filter(TaskChapter.task_id == task.id).all()
        chapters = []
        for tc in task_chapters:
            if tc.chapter:
                chapters.append(ChapterOut.model_validate(tc.chapter))

        # Get topics for this task
        task_topics = db.query(TaskTopic).filter(TaskTopic.task_id == task.id).all()
        topics = []
        for tt in task_topics:
            if tt.topic:
                topics.append(TopicOut.model_validate(tt.topic))

        # Get subtopics for this task
        task_subtopics = db.query(TaskSubTopic).filter(TaskSubTopic.task_id == task.id).all()
        subtopics = []
        for ts in task_subtopics:
            if ts.subtopic:
                subtopics.append(SubTopicOut.model_validate(ts.subtopic))

        task_dict = {
            'id': task.id,
            'name': task.name,
            'description': task.description,
            'status': task.status,
            'deadline': task.deadline,
            'accept_after_deadline': task.accept_after_deadline,
            'created_at': task.created_at,
            'updated_at': task.updated_at,
            'subject_id': task.subject_id,
            'subject': SubjectOut.model_validate(task.subject) if task.subject else None,
            'chapters': chapters,
            'topics': topics,
            'subtopics': subtopics,
        }
        task_objects.append(StudentTaskOut(**task_dict))

    total = db.query(TaskStudents).filter(TaskStudents.student_id == student_id).count()

    return MyTasks(
        tasks=task_objects,
        total=total,
        student_attachments=[StudentTaskAttachmentOut.model_validate(att) for att in student_attachments]
    )


# ==================== TASK ATTACHMENT CRUD ====================

def get_task_attachments(db: Session, task_id: UUID) -> List[TaskAttachmentOut]:
    """Get all attachments for a specific task"""
    try:
        attachments = db.query(TaskAttachment).filter(TaskAttachment.task_id == task_id).all()
        return [TaskAttachmentOut.from_orm(attachment) for attachment in attachments]
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_student_task_attachments(db: Session, task_id: UUID, student_id: UUID) -> List[StudentTaskAttachmentOut]:
    """Get all attachments for a specific task by a specific student"""
    try:
        attachments = db.query(StudentTaskAttachment).filter(
            StudentTaskAttachment.task_id == task_id,
            StudentTaskAttachment.student_id == student_id
        ).all()
        return [StudentTaskAttachmentOut.from_orm(attachment) for attachment in attachments]
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_all_student_attachments_for_task(db: Session, task_id: UUID) -> List[StudentTaskAttachmentOut]:
    """Get all student attachments for a specific task (teacher view)"""
    try:
        attachments = db.query(StudentTaskAttachment).filter(
            StudentTaskAttachment.task_id == task_id
        ).all()
        return [StudentTaskAttachmentOut.from_orm(attachment) for attachment in attachments]
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def delete_task_attachment(db: Session, attachment_id: UUID) -> bool:
    """Delete a task attachment"""
    try:
        attachment = db.query(TaskAttachment).filter(TaskAttachment.id == attachment_id).first()
        if not attachment:
            raise HTTPException(status_code=404, detail="Attachment not found")

        db.delete(attachment)
        db.commit()
        return True
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def delete_student_task_attachment(db: Session, attachment_id: UUID, student_id: UUID = None) -> bool:
    """Delete a student task attachment (with optional student permission check)"""
    try:
        query = db.query(StudentTaskAttachment).filter(StudentTaskAttachment.id == attachment_id)

        # If student_id is provided, ensure the student can only delete their own attachments
        if student_id:
            query = query.filter(StudentTaskAttachment.student_id == student_id)

        attachment = query.first()
        if not attachment:
            raise HTTPException(status_code=404, detail="Attachment not found")

        db.delete(attachment)
        db.commit()
        return True
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_tasks_by_classroom_student(db: Session, classroom_id: UUID, student_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks assigned to a specific student within a classroom"""
    task_classroom_students = db.query(TaskClassroomStudent).filter(
        TaskClassroomStudent.classroom_id == classroom_id,
        TaskClassroomStudent.student_id == student_id
    ).offset(skip).limit(limit).all()
    task_ids = [tcs.task_id for tcs in task_classroom_students]
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()
    total = db.query(TaskClassroomStudent).filter(
        TaskClassroomStudent.classroom_id == classroom_id,
        TaskClassroomStudent.student_id == student_id
    ).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_students_by_task(db: Session, task_id: UUID, skip: int = 0, limit: int = 100) -> List[TaskStudentOut]:
    """Get all students assigned to a specific task"""
    task_students = db.query(TaskStudents).filter(TaskStudents.task_id == task_id).offset(skip).limit(limit).all()
    return [TaskStudentOut.from_orm(ts) for ts in task_students]


def get_classrooms_by_task(db: Session, task_id: UUID, skip: int = 0, limit: int = 100) -> List[TaskClassroomOut]:
    """Get all classrooms assigned to a specific task"""
    task_classrooms = db.query(TaskClassroom).filter(TaskClassroom.task_id == task_id).offset(skip).limit(limit).all()
    return [TaskClassroomOut.from_orm(tc) for tc in task_classrooms]


def get_classroom_students_by_task(db: Session, task_id: UUID, skip: int = 0, limit: int = 100) -> List[TaskClassroomStudentOut]:
    """Get all classroom-student assignments for a specific task"""
    task_classroom_students = db.query(TaskClassroomStudent).filter(TaskClassroomStudent.task_id == task_id).offset(skip).limit(limit).all()
    return [TaskClassroomStudentOut.from_orm(tcs) for tcs in task_classroom_students]


# ==================== BULK ASSIGNMENT FUNCTIONS ====================

def assign_task_to_multiple_students(db: Session, task_id: UUID, student_ids: List[UUID]) -> List[TaskStudentOut]:
    """Assign task to multiple students at once"""
    try:
        with db.begin():
            # Verify task exists
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            # Verify all students exist
            students = db.query(User).filter(
                User.id.in_(student_ids),
                User.user_type == "student"
            ).all()
            if len(students) != len(student_ids):
                raise HTTPException(status_code=404, detail="One or more students not found.")

            # Check for existing assignments
            existing_assignments = db.query(TaskStudents).filter(
                TaskStudents.task_id == task_id,
                TaskStudents.student_id.in_(student_ids)
            ).all()
            if existing_assignments:
                existing_ids = [ea.student_id for ea in existing_assignments]
                raise HTTPException(status_code=400, detail=f"Task already assigned to students: {existing_ids}")

            # Create new assignments
            task_students = []
            for student_id in student_ids:
                task_student = TaskStudents(
                    task_id=task_id,
                    student_id=student_id
                )
                db.add(task_student)
                task_students.append(task_student)

            db.flush()
            return [TaskStudentOut.from_orm(ts) for ts in task_students]

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def assign_task_to_multiple_classrooms(db: Session, task_id: UUID, classroom_ids: List[UUID]) -> List[TaskClassroomOut]:
    """Assign task to multiple classrooms at once"""
    try:
        with db.begin():
            # Verify task exists
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            # Verify all classrooms exist
            classrooms = db.query(Classroom).filter(Classroom.id.in_(classroom_ids)).all()
            if len(classrooms) != len(classroom_ids):
                raise HTTPException(status_code=404, detail="One or more classrooms not found.")

            # Check for existing assignments
            existing_assignments = db.query(TaskClassroom).filter(
                TaskClassroom.task_id == task_id,
                TaskClassroom.classroom_id.in_(classroom_ids)
            ).all()
            if existing_assignments:
                existing_ids = [ea.classroom_id for ea in existing_assignments]
                raise HTTPException(status_code=400, detail=f"Task already assigned to classrooms: {existing_ids}")

            # Create new assignments
            task_classrooms = []
            for classroom_id in classroom_ids:
                task_classroom = TaskClassroom(
                    task_id=task_id,
                    classroom_id=classroom_id
                )
                db.add(task_classroom)
                task_classrooms.append(task_classroom)

            db.flush()
            return [TaskClassroomOut.from_orm(tc) for tc in task_classrooms]

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


# ==================== COMBINED TASK CREATION AND ASSIGNMENT ====================

def create_task_with_assignments(
    db: Session, 
    task_create: TaskCreateWithAssignments, 
    classroom_ids: List[UUID] = None,
    student_ids: List[UUID] = None,
    classroom_student_assignments: List[dict] = None
) -> TaskOut:
    """Create a task and assign it to classrooms and/or students simultaneously"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        # Create the task
        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        # Assign to classrooms if provided
        if classroom_ids:
            # Verify all classrooms exist
            classrooms = db.query(Classroom).filter(Classroom.id.in_(classroom_ids)).all()
            if len(classrooms) != len(classroom_ids):
                raise HTTPException(status_code=404, detail="One or more classrooms not found.")

            for classroom_id in classroom_ids:
                task_classroom = TaskClassroom(
                    task_id=new_task.id,
                    classroom_id=classroom_id
                )
                db.add(task_classroom)

        # Assign to students if provided
        if student_ids:
            # Verify all students exist
            students = db.query(User).filter(
                User.id.in_(student_ids),
                User.user_type == "student"
            ).all()
            if len(students) != len(student_ids):
                raise HTTPException(status_code=404, detail="One or more students not found.")

            for student_id in student_ids:
                task_student = TaskStudents(
                    task_id=new_task.id,
                    student_id=student_id
                )
                db.add(task_student)

        # Assign to specific classroom-student combinations if provided
        if classroom_student_assignments:
            for assignment in classroom_student_assignments:
                classroom_id = assignment.get("classroom_id")
                student_id = assignment.get("student_id")
                
                if not classroom_id or not student_id:
                    raise HTTPException(status_code=400, detail="Invalid classroom-student assignment format")

                # Verify classroom and student exist
                classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
                if not classroom:
                    raise HTTPException(status_code=404, detail=f"Classroom {classroom_id} not found.")

                student = db.query(User).filter(User.id == student_id, User.user_type == "student").first()
                if not student:
                    raise HTTPException(status_code=404, detail=f"Student {student_id} not found.")

                task_classroom_student = TaskClassroomStudent(
                    task_id=new_task.id,
                    classroom_id=classroom_id,
                    student_id=student_id
                )
                db.add(task_classroom_student)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.from_orm(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def create_task_for_student(db: Session, task_create: TaskCreateForStudent) -> TaskOut:
    """Create a task and assign it to a single student"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        # Verify student exists
        student = db.query(User).filter(User.id == task_create.student_id, User.user_type == "student").first()
        if not student:
            raise HTTPException(status_code=404, detail="Student not found.")

        # Create the task
        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        # Assign to student
        task_student = TaskStudents(
            task_id=new_task.id,
            student_id=task_create.student_id
        )
        db.add(task_student)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.model_validate(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def create_task_for_classroom(db: Session, task_create: TaskCreateForClassroom) -> TaskOut:
    """Create a task and assign it to a single classroom"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        # Verify classroom exists
        classroom = db.query(Classroom).filter(Classroom.id == task_create.classroom_id).first()
        if not classroom:
            raise HTTPException(status_code=404, detail="Classroom not found.")

        # Create the task
        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        # Assign to classroom
        task_classroom = TaskClassroom(
            task_id=new_task.id,
            classroom_id=task_create.classroom_id
        )
        db.add(task_classroom)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.model_validate(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def create_task_for_multiple_students(db: Session, task_create: TaskCreateForMultipleStudents) -> TaskOut:
    """Create a task and assign it to multiple students"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        # Verify all students exist
        students = db.query(User).filter(
            User.id.in_(task_create.student_ids),
            User.user_type == "student"
        ).all()
        if len(students) != len(task_create.student_ids):
            raise HTTPException(status_code=404, detail="One or more students not found.")

        # Create the task
        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        # Assign to students
        for student_id in task_create.student_ids:
            task_student = TaskStudents(
                task_id=new_task.id,
                student_id=student_id
            )
            db.add(task_student)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.model_validate(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def create_task_for_multiple_classrooms(db: Session, task_create: TaskCreateForMultipleClassrooms) -> TaskOut:
    """Create a task and assign it to multiple classrooms"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        # Verify all classrooms exist
        classrooms = db.query(Classroom).filter(Classroom.id.in_(task_create.classroom_ids)).all()
        if len(classrooms) != len(task_create.classroom_ids):
            raise HTTPException(status_code=404, detail="One or more classrooms not found.")

        # Create the task
        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        # Assign to classrooms
        for classroom_id in task_create.classroom_ids:
            task_classroom = TaskClassroom(
                task_id=new_task.id,
                classroom_id=classroom_id
            )
            db.add(task_classroom)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.model_validate(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


# ==================== SEARCH AND FILTER FUNCTIONS ====================

def get_tasks_by_student_minimal(db: Session, student_id: UUID, skip: int = 0, limit: int = 100) -> MyTasksMinimal:
    """Get minimal task info for student - used in task lists"""
    task_students = db.query(TaskStudents).filter(TaskStudents.student_id == student_id).offset(skip).limit(limit).all()
    task_ids = [ts.task_id for ts in task_students]
    
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()
    
    task_objects = []
    for task in tasks:
        task_objects.append(StudentTaskMinimalOut(
            id=task.id,
            name=task.name,
            description=task.description,
            deadline=task.deadline,
            status=task.status,
            accept_after_deadline=task.accept_after_deadline,
            subject=SubjectOut.model_validate(task.subject) if task.subject else None
        ))
    
    total = db.query(TaskStudents).filter(TaskStudents.student_id == student_id).count()
    
    return MyTasksMinimal(tasks=task_objects, total=total)


def get_task_by_id_for_student(db: Session, task_id: UUID, student_id: UUID) -> StudentTaskDetailedOut:
    """Get detailed task info for student including all attachments"""
    try:
        # Check if student is assigned to this task
        task_student = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.student_id == student_id
        ).first()
        
        if not task_student:
            raise HTTPException(status_code=404, detail="Task not found or not assigned to you.")
        
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Get teacher attachments
        teacher_attachments = db.query(TaskAttachment).filter(TaskAttachment.task_id == task_id).all()
        
        # Get student's own attachments
        student_attachments = db.query(StudentTaskAttachment).filter(
            StudentTaskAttachment.task_id == task_id,
            StudentTaskAttachment.student_id == student_id
        ).all()

        return StudentTaskDetailedOut(
            id=task.id,
            name=task.name,
            description=task.description,
            status=task.status,
            deadline=task.deadline,
            accept_after_deadline=task.accept_after_deadline,
            created_at=task.created_at,
            updated_at=task.updated_at,
            subject=SubjectOut.model_validate(task.subject) if task.subject else None,
            chapters=[ChapterOut.model_validate(tc.chapter) for tc in getattr(task, 'chapters', [])],
            topics=[TopicOut.model_validate(tt.topic) for tt in getattr(task, 'topics', [])],
            subtopics=[SubTopicOut.model_validate(ts.subtopic) for ts in getattr(task, 'subtopics', [])],
            teacher_attachments=[TaskAttachmentOut.model_validate(att) for att in teacher_attachments],
            my_attachments=[StudentTaskAttachmentOut.model_validate(att) for att in student_attachments]
        )

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
