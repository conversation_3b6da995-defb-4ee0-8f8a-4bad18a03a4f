from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID
from Schemas.Exams.Exam import (
    ExamCreate, ExamUpdate, ExamOut, ExamCreateWithAssignment, ExamAssignmentOut,
    ExamStudentOut, ExamStudentMinimalOut, ExamListOut, ExamDetailedOut,
    ExamAssignmentRequest, ExamUnassignmentRequest, ExamAssignmentResponse, ExamAssignmentDetailedResponse,
    ExamComprehensiveDetails, AvailableStudent, ExamStatistics, ExamMarksStatistics, TopPerformer
)
from Cruds.Exams.Exam import (
    get_exam_by_id,
    get_exam_by_id_comprehensive,
    get_all_exams,
    update_exam,
    delete_exam,
    create_exam_with_assignment,
    get_exams_by_teacher,
    get_all_exams_for_student,
    get_exam_for_student,
    get_all_exams_minimal,
    get_exams_by_teacher_minimal,
    assign_exam_to_students_and_classrooms,
    get_exam_assignments,
    unassign_exam_from_students,
    get_available_students_for_assignment,
    calculate_exam_statistics
)
from config.session import get_db
from typing import List
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type
from Models.Questions import Question
from datetime import timedelta, datetime, timezone

router = APIRouter()

def get_utc_now():
    """Get current time in UTC"""
    return datetime.now(timezone.utc)

def ensure_utc_timezone(dt):
    """Ensure datetime is in UTC timezone"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    else:
        return dt.astimezone(timezone.utc)

def is_teacher_exam(db: Session, exam_id: UUID, teacher_id) -> bool:
    # Ensure teacher_id is a value, not a Column
    question = db.query(Question).filter(
        Question.teacher_id == teacher_id,
        Question.exams.any(id=exam_id)
    ).first()
    return question is not None

@router.get("/", response_model=List[ExamOut])
def read_exams(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))):
    return get_all_exams(db)

@router.get("/my-exams", response_model=List[ExamOut])
def get_teacher_exams(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    if teacher_id is None:
        raise HTTPException(status_code=401, detail="Invalid user.")
    return get_exams_by_teacher(db, teacher_id)

@router.get("/list/all", response_model=List[ExamListOut])
def get_all_exams_list(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get all exams with minimal information for listing purposes.

    Query Parameters:
    - skip: Number of records to skip (pagination)
    - limit: Maximum number of records to return (default: 100)

    Returns:
    - List of exams with basic info: id, title, description, total_marks,
      total_duration, start_time, end_time, total_questions, timestamps
    - No question objects included for better performance
    """
    exams = get_all_exams_minimal(db, skip=skip, limit=limit)
    return [ExamListOut.from_orm_with_calculated_fields(exam) for exam in exams]

@router.get("/my-exams/list", response_model=List[ExamListOut])
def get_teacher_exams_list(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Get all exams created by the current teacher with minimal information.

    Query Parameters:
    - skip: Number of records to skip (pagination)
    - limit: Maximum number of records to return (default: 100)

    Returns:
    - List of teacher's exams with basic info: id, title, description, total_marks,
      total_duration, start_time, end_time, total_questions, timestamps
    - No question objects included for better performance
    - Only includes exams that contain at least one question created by the teacher
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    if teacher_id is None:
        raise HTTPException(status_code=401, detail="Invalid user.")

    exams = get_exams_by_teacher_minimal(db, teacher_id, skip=skip, limit=limit)
    return [ExamListOut.from_orm_with_calculated_fields(exam) for exam in exams]

@router.get("/{exam_id}", response_model=ExamDetailedOut)
def read_exam(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Get comprehensive exam details including assigned students and class information.

    Returns:
    - Complete exam information with questions
    - List of assigned students with their details
    - Assignment tracking information (individual vs classroom)
    - Class number extracted from questions
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    return get_exam_by_id_comprehensive(db, exam_id, teacher_id=teacher_id)

@router.post("/", response_model=ExamOut)
def create_exam_endpoint(
    exam_in: ExamCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    # This endpoint is not implemented in the CRUD, so raise an error or implement if needed
    raise HTTPException(status_code=501, detail="Not implemented. Use /create-with-assignment instead.")

@router.put("/{exam_id}", response_model=ExamOut)
def update_exam_endpoint(
    exam_id: UUID,
    exam_update: ExamUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    return update_exam(db, exam_id, exam_update, teacher_id=teacher_id)

@router.delete("/{exam_id}", status_code=204)
def delete_exam_endpoint(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    delete_exam(db, exam_id, teacher_id=teacher_id)
    return None

@router.post("/create-with-assignment", response_model=ExamAssignmentOut)
def create_exam_with_assignment_endpoint(
    exam_in: ExamCreateWithAssignment,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token, db)
    return create_exam_with_assignment(db, exam_in, current_user)

@router.get("/student/all", response_model=List[ExamStudentMinimalOut])
def get_student_all_exams(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Get ALL assigned exams for the current student.
    Returns upcoming, ongoing, and completed exams.
    Client can filter by time/status as needed.
    """
    current_user = get_current_user(token, db)
    student_id = getattr(current_user, 'id', None)
    if student_id is None:
        raise HTTPException(status_code=401, detail="Invalid user.")
    exams = get_all_exams_for_student(db, student_id)
    result = []
    for exam in exams:
        start_time = getattr(exam, 'start_time', None)
        total_duration = getattr(exam, 'total_duration', None)
        end_time = None
        if start_time and total_duration:
            end_time = start_time + timedelta(minutes=int(total_duration))

        # Get status from the enhanced CRUD function
        status = getattr(exam, 'status', 'assigned')

        result.append(ExamStudentMinimalOut(
            id=getattr(exam, 'id', None),
            title=getattr(exam, 'title', None),
            description=getattr(exam, 'description', None),
            total_marks=getattr(exam, 'total_marks', 0),
            start_time=start_time,
            end_time=end_time,
            total_duration=int(total_duration) if total_duration is not None else 0,
            status=status
        ))
    return result

@router.get("/student/upcoming", response_model=List[ExamStudentMinimalOut])
def get_student_upcoming_exams_deprecated(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    DEPRECATED: Use /student/all instead.
    This endpoint now returns all exams (same as /student/all).
    """
    return get_student_all_exams(db, token, _)

@router.get("/student/{exam_id}", response_model=ExamStudentOut)
def get_student_exam(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    current_user = get_current_user(token, db)
    student_id = getattr(current_user, 'id', None)
    if student_id is None:
        raise HTTPException(status_code=401, detail="Invalid user.")
    exam = get_exam_for_student(db, exam_id, student_id)
    now = get_utc_now()
    start_time = getattr(exam, 'start_time', None)
    total_duration = getattr(exam, 'total_duration', None)

    # Ensure timezone compatibility for comparison
    if start_time:
        start_time_utc = ensure_utc_timezone(start_time)
        if now < start_time_utc:
            raise HTTPException(status_code=403, detail="Exam not started yet.")
    # Calculate end_time using timezone-aware start_time
    end_time = None
    if start_time and total_duration:
        start_time_utc = ensure_utc_timezone(start_time)
        end_time = start_time_utc + timedelta(minutes=int(total_duration))

    return ExamStudentOut(
        id=getattr(exam, 'id', None),
        title=getattr(exam, 'title', ''),
        description=getattr(exam, 'description', None),
        total_marks=getattr(exam, 'total_marks', 0),
        total_duration=getattr(exam, 'total_duration', 0),
        questions=[q for q in exam.questions],
        start_time=ensure_utc_timezone(start_time) if start_time else None,
        end_time=end_time
    )


# ===== EXAM ASSIGNMENT ENDPOINTS =====

@router.post("/{exam_id}/assign", response_model=ExamAssignmentResponse)
def assign_exam(
    exam_id: UUID,
    assignment_request: ExamAssignmentRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Assign an existing exam to students and/or classrooms.

    **Actions:**
    - `add`: Add new assignments (default)
    - `remove`: Remove specified assignments
    - `replace`: Replace all current assignments with new ones

    **Assignment Options:**
    - Assign to individual students using `student_ids`
    - Assign to entire classrooms using `classroom_ids`
    - Combine both for mixed assignments

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)

    # Validate that at least one assignment target is provided
    if not assignment_request.student_ids and not assignment_request.classroom_ids:
        raise HTTPException(
            status_code=400,
            detail="At least one of student_ids or classroom_ids must be provided"
        )

    # Perform the assignment
    result = assign_exam_to_students_and_classrooms(
        db=db,
        exam_id=exam_id,
        student_ids=assignment_request.student_ids,
        classroom_ids=assignment_request.classroom_ids,
        teacher_id=teacher_id,
        action=assignment_request.action
    )

    # Create response message
    action_messages = {
        "add": f"Successfully assigned exam to {len(result.assigned_student_ids)} students",
        "remove": f"Successfully removed exam assignment from students. {len(result.assigned_student_ids)} students remain assigned",
        "replace": f"Successfully replaced exam assignments. Now assigned to {len(result.assigned_student_ids)} students"
    }

    return ExamAssignmentResponse(
        exam_id=result.exam_id,
        assigned_student_ids=result.assigned_student_ids,
        total_assigned=len(result.assigned_student_ids),
        action_performed=assignment_request.action,
        message=action_messages.get(assignment_request.action, "Assignment completed")
    )


@router.delete("/{exam_id}/unassign", response_model=ExamAssignmentResponse)
def unassign_exam(
    exam_id: UUID,
    unassignment_request: ExamUnassignmentRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Remove exam assignment from specific students.

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)

    # Perform the unassignment
    result = unassign_exam_from_students(
        db=db,
        exam_id=exam_id,
        student_ids=unassignment_request.student_ids,
        teacher_id=teacher_id
    )

    return ExamAssignmentResponse(
        exam_id=result.exam_id,
        assigned_student_ids=result.assigned_student_ids,
        total_assigned=len(result.assigned_student_ids),
        action_performed="remove",
        message=f"Successfully removed exam assignment from {len(unassignment_request.student_ids)} students"
    )


@router.get("/{exam_id}/assignments", response_model=ExamAssignmentOut)
def get_exam_assignment_details(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Get current assignment details for an exam.

    Returns list of all students currently assigned to the exam.

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)

    return get_exam_assignments(db=db, exam_id=exam_id, teacher_id=teacher_id)


@router.get("/{exam_id}/comprehensive", response_model=ExamComprehensiveDetails)
def get_comprehensive_exam_details(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Get comprehensive exam details including:
    - Basic exam information (title, description, duration, etc.)
    - All exam questions
    - Current assignment details (which students are assigned)
    - Available students for assignment (from teacher's classrooms)
    - Comprehensive statistics:
      * Completion rate
      * Total submissions
      * Marks statistics (highest, lowest, average, standard deviation)
      * Top performers

    This endpoint provides everything needed for exam management and analytics.

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)

    # Get basic exam details
    exam = get_exam_by_id_comprehensive(db=db, exam_id=exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Verify teacher ownership
    if exam.questions:
        question = exam.questions[0]
        if hasattr(question, 'teacher_id') and question.teacher_id != teacher_id:
            raise HTTPException(status_code=403, detail="You do not have permission to view this exam")

    # Get assignment details
    assignment_details = get_exam_assignments(db=db, exam_id=exam_id, teacher_id=teacher_id)

    # Get available students for assignment
    available_students_data = get_available_students_for_assignment(
        db=db,
        teacher_id=teacher_id,
        exam_id=exam_id
    )
    available_students = [AvailableStudent(**student) for student in available_students_data]

    # Calculate statistics
    stats_data = calculate_exam_statistics(db=db, exam_id=exam_id)

    # Format statistics
    marks_stats = ExamMarksStatistics(**stats_data["marks_statistics"])
    top_performers = [TopPerformer(**performer) for performer in stats_data["top_performers"]]

    statistics = ExamStatistics(
        total_assigned=stats_data["total_assigned"],
        total_attempts=stats_data["total_attempts"],
        completed_attempts=stats_data["completed_attempts"],
        completion_rate=stats_data["completion_rate"],
        marks_statistics=marks_stats,
        top_performers=top_performers
    )

    # Build comprehensive response
    return ExamComprehensiveDetails(
        id=exam.id,
        title=exam.title,
        description=exam.description,
        total_marks=exam.total_marks,
        total_duration=exam.total_duration,
        start_time=exam.start_time,
        questions=exam.questions,
        assignment_details=assignment_details,
        available_students=available_students,
        statistics=statistics
    )


@router.get("/{exam_id}/available-students", response_model=List[AvailableStudent])
def get_available_students_for_exam_assignment(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Get all students available for assignment to this exam.

    Returns students from the teacher's classrooms with their current assignment status.
    This is useful for assignment management interfaces.

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)

    # Verify exam exists and teacher has permission
    exam = get_exam_by_id(db=db, exam_id=exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Get available students
    available_students_data = get_available_students_for_assignment(
        db=db,
        teacher_id=teacher_id,
        exam_id=exam_id
    )

    return [AvailableStudent(**student) for student in available_students_data]


@router.get("/{exam_id}/statistics", response_model=ExamStatistics)
def get_exam_statistics_only(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Get comprehensive statistics for an exam.

    Returns detailed analytics including:
    - Completion rates
    - Submission counts
    - Marks statistics (highest, lowest, average, standard deviation)
    - Top performers

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)

    # Verify exam exists and teacher has permission
    exam = get_exam_by_id(db=db, exam_id=exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Calculate statistics
    stats_data = calculate_exam_statistics(db=db, exam_id=exam_id)

    # Format statistics
    marks_stats = ExamMarksStatistics(**stats_data["marks_statistics"])
    top_performers = [TopPerformer(**performer) for performer in stats_data["top_performers"]]

    return ExamStatistics(
        total_assigned=stats_data["total_assigned"],
        total_attempts=stats_data["total_attempts"],
        completed_attempts=stats_data["completed_attempts"],
        completion_rate=stats_data["completion_rate"],
        marks_statistics=marks_stats,
        top_performers=top_performers
    )